import { VideoDownloader } from "@/components/video-downloader";
import { Head<PERSON> } from "@/components/header";
import { Footer } from "@/components/footer";
// import { MonetizedLayout } from "@/components/monetization/monetized-layout";
import { HeaderAd, FooterAd } from "@/components/ads/google-adsense";
import { SEOContent } from "@/components/seo/seo-content";
import { Metadata } from "next";

export const metadata: Metadata = {
  title:
    "Free YouTube Video Downloader | Download HD Videos Online - VideoBoom",
  description:
    "Download YouTube videos in HD quality for free. Fast, secure, and easy-to-use online YouTube downloader. Supports MP4, MP3, 1080p, 4K downloads. No registration required.",
  alternates: {
    canonical: "https://videoboom.com",
  },
};

export default function Home() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      {/* Header Ad */}
      {process.env.NEXT_PUBLIC_GOOGLE_ADSENSE_CLIENT_ID && (
        <div className="w-full bg-white dark:bg-gray-800 border-b">
          <div className="container mx-auto px-4 py-2">
            <HeaderAd />
          </div>
        </div>
      )}

      <main className="flex-1">
        <div className="container mx-auto px-4 py-8">
          {/* Main Video Downloader */}
          <VideoDownloader />

          {/* SEO Content */}
          <SEOContent />
        </div>
      </main>

      {/* Footer Ad */}
      {process.env.NEXT_PUBLIC_GOOGLE_ADSENSE_CLIENT_ID && (
        <div className="w-full bg-white dark:bg-gray-800 border-t">
          <div className="container mx-auto px-4 py-4">
            <div className="text-center text-sm text-gray-500 mb-2">
              Advertisement
            </div>
            <FooterAd />
          </div>
        </div>
      )}

      <Footer />
    </div>
  );
}
