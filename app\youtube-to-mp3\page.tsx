import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { VideoDownloader } from "@/components/video-downloader";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "YouTube to MP3 Converter | Download YouTube Audio as MP3 - VideoBoom",
  description: "Convert YouTube videos to MP3 audio for free. High-quality YouTube to MP3 converter with 320kbps audio. Extract audio from YouTube videos instantly.",
  keywords: [
    "youtube to mp3",
    "youtube mp3 converter",
    "download youtube audio",
    "youtube audio downloader",
    "convert youtube to mp3",
    "youtube mp3 extractor",
    "free youtube to mp3"
  ],
  alternates: {
    canonical: "https://videoboom.com/youtube-to-mp3",
  },
  openGraph: {
    title: "YouTube to MP3 Converter | Download YouTube Audio as MP3",
    description: "Convert YouTube videos to MP3 audio for free. High-quality YouTube to MP3 converter with 320kbps audio.",
    url: "https://videoboom.com/youtube-to-mp3",
    type: "website",
  },
};

export default function YouTubeToMP3Page() {
  return (
    <>
      <Header />
      <div className="min-h-screen flex flex-col">
        <main className="flex-1">
          <div className="container mx-auto px-4 py-8">
            {/* SEO Content */}
            <div className="max-w-4xl mx-auto text-center mb-8">
              <h1 className="text-4xl font-bold mb-4 text-gray-900 dark:text-gray-100">
                YouTube to MP3 Converter - Extract Audio from YouTube Videos
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-6">
                Convert YouTube videos to MP3 audio format for free. Our YouTube to MP3 converter extracts high-quality audio up to 320kbps from any YouTube video.
              </p>
            </div>

            {/* Video Downloader Component */}
            <VideoDownloader />

            {/* Additional SEO Content */}
            <div className="max-w-4xl mx-auto mt-12 space-y-8">
              <section>
                <h2 className="text-3xl font-bold mb-4 text-gray-900 dark:text-gray-100">
                  Why Use Our YouTube to MP3 Converter?
                </h2>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-xl font-semibold mb-2">High Quality Audio</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Extract audio from YouTube videos in high quality up to 320kbps MP3 format.
                    </p>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2">Fast Audio Extraction</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Quick YouTube to MP3 conversion with our optimized audio processing servers.
                    </p>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2">Perfect for Music</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Ideal for downloading music, podcasts, and audio content from YouTube.
                    </p>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2">All Devices Supported</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      MP3 files work on all music players, phones, computers, and audio devices.
                    </p>
                  </div>
                </div>
              </section>

              <section>
                <h2 className="text-3xl font-bold mb-4 text-gray-900 dark:text-gray-100">
                  How to Convert YouTube to MP3
                </h2>
                <ol className="list-decimal list-inside space-y-3 text-gray-600 dark:text-gray-300">
                  <li>Copy the YouTube video URL containing the audio you want</li>
                  <li>Paste the URL into our YouTube to MP3 converter above</li>
                  <li>Select MP3 format and choose your preferred audio quality</li>
                  <li>Click download to save the MP3 audio file to your device</li>
                </ol>
              </section>

              <section>
                <h2 className="text-3xl font-bold mb-4 text-gray-900 dark:text-gray-100">
                  MP3 Audio Quality Options
                </h2>
                <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                  <li>• 320kbps MP3 - Highest Quality Audio</li>
                  <li>• 256kbps MP3 - High Quality Audio</li>
                  <li>• 192kbps MP3 - Good Quality Audio</li>
                  <li>• 128kbps MP3 - Standard Quality Audio</li>
                </ul>
              </section>

              <section>
                <h2 className="text-3xl font-bold mb-4 text-gray-900 dark:text-gray-100">
                  Popular Uses for YouTube to MP3
                </h2>
                <ul className="grid md:grid-cols-2 gap-3 text-gray-600 dark:text-gray-300">
                  <li>• Download music from YouTube</li>
                  <li>• Extract podcast audio</li>
                  <li>• Save educational content audio</li>
                  <li>• Create offline music playlists</li>
                  <li>• Extract audio for presentations</li>
                  <li>• Save meditation and relaxation audio</li>
                </ul>
              </section>
            </div>
          </div>
        </main>
      </div>
      <Footer />
    </>
  );
}
