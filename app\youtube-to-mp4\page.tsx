import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { VideoDownloader } from "@/components/video-downloader";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "YouTube to MP4 Converter | Download YouTube Videos as MP4 - VideoBoom",
  description: "Convert YouTube videos to MP4 format for free. High-quality YouTube to MP4 converter supporting 1080p, 4K downloads. Fast, secure, and easy to use.",
  keywords: [
    "youtube to mp4",
    "youtube mp4 converter",
    "download youtube as mp4",
    "youtube video to mp4",
    "convert youtube to mp4",
    "youtube mp4 downloader",
    "free youtube to mp4"
  ],
  alternates: {
    canonical: "https://videoboom.com/youtube-to-mp4",
  },
  openGraph: {
    title: "YouTube to MP4 Converter | Download YouTube Videos as MP4",
    description: "Convert YouTube videos to MP4 format for free. High-quality YouTube to MP4 converter supporting 1080p, 4K downloads.",
    url: "https://videoboom.com/youtube-to-mp4",
    type: "website",
  },
};

export default function YouTubeToMP4Page() {
  return (
    <>
      <Header />
      <div className="min-h-screen flex flex-col">
        <main className="flex-1">
          <div className="container mx-auto px-4 py-8">
            {/* SEO Content */}
            <div className="max-w-4xl mx-auto text-center mb-8">
              <h1 className="text-4xl font-bold mb-4 text-gray-900 dark:text-gray-100">
                YouTube to MP4 Converter - Download Videos in MP4 Format
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-6">
                Convert and download YouTube videos to MP4 format for free. Our YouTube to MP4 converter supports high-quality downloads including 1080p, 1440p, and 4K resolution.
              </p>
            </div>

            {/* Video Downloader Component */}
            <VideoDownloader />

            {/* Additional SEO Content */}
            <div className="max-w-4xl mx-auto mt-12 space-y-8">
              <section>
                <h2 className="text-3xl font-bold mb-4 text-gray-900 dark:text-gray-100">
                  Why Choose Our YouTube to MP4 Converter?
                </h2>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-xl font-semibold mb-2">High Quality MP4 Downloads</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Download YouTube videos in original MP4 quality up to 4K resolution. Preserve the video quality you love.
                    </p>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2">Fast Conversion</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Our optimized servers ensure lightning-fast YouTube to MP4 conversion and download speeds.
                    </p>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2">Universal Compatibility</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      MP4 format works on all devices - Windows, Mac, Android, iOS, and smart TVs.
                    </p>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2">No Software Required</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Convert YouTube to MP4 directly in your browser. No downloads or installations needed.
                    </p>
                  </div>
                </div>
              </section>

              <section>
                <h2 className="text-3xl font-bold mb-4 text-gray-900 dark:text-gray-100">
                  How to Convert YouTube to MP4
                </h2>
                <ol className="list-decimal list-inside space-y-3 text-gray-600 dark:text-gray-300">
                  <li>Copy the YouTube video URL from your browser</li>
                  <li>Paste the URL into our YouTube to MP4 converter above</li>
                  <li>Select MP4 format and your preferred quality (1080p, 4K, etc.)</li>
                  <li>Click download to save the MP4 video to your device</li>
                </ol>
              </section>

              <section>
                <h2 className="text-3xl font-bold mb-4 text-gray-900 dark:text-gray-100">
                  Supported MP4 Quality Options
                </h2>
                <ul className="grid md:grid-cols-2 gap-3 text-gray-600 dark:text-gray-300">
                  <li>• 4K MP4 (2160p) - Ultra High Definition</li>
                  <li>• 1440p MP4 - Quad HD Quality</li>
                  <li>• 1080p MP4 - Full HD Quality</li>
                  <li>• 720p MP4 - HD Quality</li>
                  <li>• 480p MP4 - Standard Definition</li>
                  <li>• 360p MP4 - Mobile Friendly</li>
                </ul>
              </section>
            </div>
          </div>
        </main>
      </div>
      <Footer />
    </>
  );
}
