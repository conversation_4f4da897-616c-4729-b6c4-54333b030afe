import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "How to Download YouTube Videos - Step by Step Guide | VideoBoom",
  description: "Learn how to download YouTube videos in 3 easy steps. Complete guide with screenshots and tips for downloading HD videos, MP4, MP3 formats.",
  alternates: {
    canonical: "https://videoboom.com/how-to-download",
  },
};

export default function HowToDownloadPage() {
  return (
    <>
      <Header />
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-bold text-center mb-8 text-gray-900 dark:text-gray-100">
              How to Download YouTube Videos - Complete Guide
            </h1>
            
            <div className="prose prose-lg max-w-none dark:prose-invert mb-12">
              <p className="text-xl text-gray-600 dark:text-gray-300 text-center mb-8">
                Follow our simple 3-step process to download any YouTube video in high quality. 
                No software installation required!
              </p>
            </div>

            {/* Step-by-step guide */}
            <div className="space-y-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-4">
                    <div className="bg-blue-500 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold">
                      1
                    </div>
                    Copy the YouTube Video URL
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p>Navigate to YouTube and find the video you want to download. Copy the video URL from your browser's address bar.</p>
                  <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                    <p className="font-mono text-sm">Example: https://www.youtube.com/watch?v=dQw4w9WgXcQ</p>
                  </div>
                  <ul className="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-300">
                    <li>You can copy the URL from the address bar</li>
                    <li>Or use the "Share" button and copy the link</li>
                    <li>Works with any YouTube video URL format</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-4">
                    <div className="bg-green-500 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold">
                      2
                    </div>
                    Paste URL and Select Quality
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p>Paste the copied URL into VideoBoom's input field and choose your preferred video quality and format.</p>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold mb-2">Video Formats:</h4>
                      <ul className="list-disc list-inside space-y-1 text-sm text-gray-600 dark:text-gray-300">
                        <li>MP4 (recommended)</li>
                        <li>WebM</li>
                        <li>3GP</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Audio Formats:</h4>
                      <ul className="list-disc list-inside space-y-1 text-sm text-gray-600 dark:text-gray-300">
                        <li>MP3 (most popular)</li>
                        <li>M4A</li>
                        <li>WebM Audio</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-4">
                    <div className="bg-purple-500 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold">
                      3
                    </div>
                    Download and Save
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p>Click the download button and save the video to your device. The download will start automatically.</p>
                  <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 p-4 rounded-lg">
                    <h4 className="font-semibold text-green-800 dark:text-green-200 mb-2">💡 Pro Tips:</h4>
                    <ul className="list-disc list-inside space-y-1 text-sm text-green-700 dark:text-green-300">
                      <li>Choose MP4 format for best compatibility</li>
                      <li>Select 1080p for high quality videos</li>
                      <li>Use MP3 format for music and audio content</li>
                      <li>Check your download folder after completion</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Additional tips */}
            <div className="mt-12">
              <h2 className="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-gray-100">
                Troubleshooting & Tips
              </h2>
              
              <div className="grid md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Download Not Starting?</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                      <li>• Check if the YouTube URL is correct</li>
                      <li>• Try refreshing the page</li>
                      <li>• Disable ad blockers temporarily</li>
                      <li>• Clear your browser cache</li>
                    </ul>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Best Practices</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                      <li>• Only download videos you have permission for</li>
                      <li>• Respect copyright and fair use policies</li>
                      <li>• Choose appropriate quality for your needs</li>
                      <li>• Keep your downloads organized</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* CTA */}
            <div className="text-center mt-12">
              <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-gray-100">
                Ready to Download YouTube Videos?
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Start downloading your favorite YouTube videos in high quality right now!
              </p>
              <a 
                href="/"
                className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg transition-colors"
              >
                Start Downloading Now
              </a>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}
