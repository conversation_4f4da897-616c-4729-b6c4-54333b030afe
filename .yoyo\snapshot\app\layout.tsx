import type React from "react";
import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import { AdSenseScript } from "@/components/ads/adsense-script";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "VideoBoom - YouTube Video Downloader",
  description:
    "Fast, free, and easy way to download YouTube videos in high quality. Supports MP4, MP3, and multiple resolutions.",
  keywords:
    "youtube downloader, video downloader, mp4 download, mp3 converter, free video download",
  icons: {
    icon: "/favicon.svg",
  },
  openGraph: {
    title: "VideoBoom - Free YouTube Video Downloader",
    description: "Download YouTube videos in high quality for free",
    type: "website",
  },
  generator: "v0.dev",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        {process.env.NEXT_PUBLIC_GOOGLE_ADSENSE_CLIENT_ID && (
          <AdSenseScript
            clientId={process.env.NEXT_PUBLIC_GOOGLE_ADSENSE_CLIENT_ID}
          />
        )}
      </head>
      <body className={inter.className}>
        {children}
        <Toaster />
      </body>
    </html>
  );
}
