import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "../ui/card";

export function SEOContent() {
  return (
    <div className="space-y-8 mt-12">
      {/* Hero SEO Section */}
      <section className="text-center">
        <h1 className="text-4xl font-bold mb-4 text-gray-900 dark:text-gray-100">
          Free YouTube Video Downloader - Download HD Videos Online
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Download YouTube videos in high quality for free with VideoBoom. Our fast, secure, and easy-to-use online YouTube downloader supports MP4, MP3, 1080p, and 4K downloads. No registration required.
        </p>
      </section>

      {/* Features Section */}
      <section>
        <h2 className="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-gray-100">
          Why Choose VideoBoom YouTube Downloader?
        </h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🚀 Lightning Fast Downloads
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-300">
                Download YouTube videos at maximum speed with our optimized servers. Get your favorite videos in seconds, not minutes.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🎥 HD & 4K Quality Support
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-300">
                Download videos in original quality including 1080p, 1440p, and 4K resolution. Preserve the video quality you love.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🔒 100% Safe & Secure
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-300">
                No malware, no viruses, no registration required. Your privacy is protected with our secure download process.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                📱 Works on All Devices
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-300">
                Compatible with Windows, Mac, Android, and iOS. Download YouTube videos on any device with a web browser.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🎵 MP3 Audio Extraction
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-300">
                Extract high-quality audio from YouTube videos and save as MP3 files. Perfect for music and podcasts.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                💯 Completely Free
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-300">
                No hidden fees, no premium subscriptions. Download unlimited YouTube videos absolutely free forever.
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* How to Use Section */}
      <section>
        <h2 className="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-gray-100">
          How to Download YouTube Videos in 3 Easy Steps
        </h2>
        <div className="grid md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl font-bold text-blue-600 dark:text-blue-300">1</span>
            </div>
            <h3 className="text-xl font-semibold mb-2">Copy YouTube URL</h3>
            <p className="text-gray-600 dark:text-gray-300">
              Copy the URL of the YouTube video you want to download from your browser's address bar.
            </p>
          </div>
          <div className="text-center">
            <div className="bg-green-100 dark:bg-green-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl font-bold text-green-600 dark:text-green-300">2</span>
            </div>
            <h3 className="text-xl font-semibold mb-2">Paste & Select Quality</h3>
            <p className="text-gray-600 dark:text-gray-300">
              Paste the URL into our downloader and choose your preferred video quality and format.
            </p>
          </div>
          <div className="text-center">
            <div className="bg-purple-100 dark:bg-purple-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl font-bold text-purple-600 dark:text-purple-300">3</span>
            </div>
            <h3 className="text-xl font-semibold mb-2">Download & Enjoy</h3>
            <p className="text-gray-600 dark:text-gray-300">
              Click download and save the video to your device. It's that simple!
            </p>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section>
        <h2 className="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-gray-100">
          Frequently Asked Questions
        </h2>
        <div className="space-y-4 max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Is it legal to download YouTube videos?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-300">
                Downloading YouTube videos for personal use is generally acceptable, but you should respect copyright laws and YouTube's terms of service. Only download videos you have permission to download or that are in the public domain.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>What video qualities are supported?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-300">
                We support all available YouTube video qualities including 144p, 240p, 360p, 480p, 720p, 1080p, 1440p, and 4K (2160p) when available from the original video.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Do I need to install any software?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-300">
                No! VideoBoom is a web-based YouTube downloader that works directly in your browser. No software installation or registration required.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Is there a download limit?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-300">
                No, there are no download limits. You can download as many YouTube videos as you want, completely free of charge.
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Keywords Section for SEO */}
      <section className="bg-gray-50 dark:bg-gray-800 rounded-lg p-8">
        <h2 className="text-2xl font-bold text-center mb-6 text-gray-900 dark:text-gray-100">
          Popular YouTube Download Searches
        </h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
          <div className="space-y-2">
            <h3 className="font-semibold text-gray-800 dark:text-gray-200">Video Formats</h3>
            <ul className="space-y-1 text-gray-600 dark:text-gray-300">
              <li>• YouTube to MP4</li>
              <li>• YouTube to AVI</li>
              <li>• YouTube to MOV</li>
              <li>• YouTube to WMV</li>
            </ul>
          </div>
          <div className="space-y-2">
            <h3 className="font-semibold text-gray-800 dark:text-gray-200">Audio Formats</h3>
            <ul className="space-y-1 text-gray-600 dark:text-gray-300">
              <li>• YouTube to MP3</li>
              <li>• YouTube to WAV</li>
              <li>• YouTube to FLAC</li>
              <li>• YouTube to AAC</li>
            </ul>
          </div>
          <div className="space-y-2">
            <h3 className="font-semibold text-gray-800 dark:text-gray-200">Quality Options</h3>
            <ul className="space-y-1 text-gray-600 dark:text-gray-300">
              <li>• 4K Video Download</li>
              <li>• 1080p HD Download</li>
              <li>• 720p Download</li>
              <li>• High Quality Audio</li>
            </ul>
          </div>
        </div>
      </section>
    </div>
  );
}
