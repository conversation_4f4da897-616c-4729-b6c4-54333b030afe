// Test script to verify the download fixes
const { spawn } = require("child_process");

async function testVideoInfo(url) {
  console.log("Testing video info extraction...");

  return new Promise((resolve, reject) => {
    const ytDlp = spawn(
      "python",
      ["-m", "yt_dlp", "--list-formats", "--dump-json", url],
      {
        stdio: ["pipe", "pipe", "pipe"],
      }
    );

    let stdout = "";
    let stderr = "";

    ytDlp.stdout.on("data", (data) => {
      stdout += data.toString();
    });

    ytDlp.stderr.on("data", (data) => {
      stderr += data.toString();
    });

    ytDlp.on("close", (code) => {
      if (code === 0) {
        try {
          const lines = stdout.trim().split("\n");
          const videoInfo = JSON.parse(lines[lines.length - 1]);

          console.log("Video Title:", videoInfo.title);
          console.log("Duration:", videoInfo.duration);

          // Extract video formats
          const videoFormats = videoInfo.formats.filter(
            (fmt) =>
              fmt.vcodec !== "none" &&
              fmt.height &&
              fmt.ext === "mp4" &&
              !fmt.format_note?.includes("storyboard")
          );

          console.log("\nAvailable video formats:");
          videoFormats
            .sort((a, b) => b.height - a.height)
            .slice(0, 10)
            .forEach((fmt) => {
              console.log(
                `${fmt.format_id}: ${fmt.height}p (${fmt.width}x${fmt.height}) - ${fmt.ext} - ${fmt.vcodec}`
              );
            });

          resolve(videoInfo);
        } catch (error) {
          console.error("Error parsing yt-dlp output:", error);
          reject(new Error("Failed to parse video information"));
        }
      } else {
        console.error("yt-dlp error:", stderr);
        reject(new Error("Failed to fetch video information"));
      }
    });
  });
}

async function testDownload(url, quality = "1080p") {
  console.log(`\nTesting download with quality: ${quality}...`);

  const maxHeight = quality.replace("p", "");
  const formatSelector = `bestvideo[height=${maxHeight}][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height=${maxHeight}]+bestaudio/bestvideo[height<=${maxHeight}][ext=mp4][vcodec^=avc1]+bestaudio[ext=m4a]/bestvideo[height<=${maxHeight}][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height<=${maxHeight}]+bestaudio/best[height=${maxHeight}][ext=mp4]/best[height<=${maxHeight}][ext=mp4]/best[height<=${maxHeight}]/best`;

  return new Promise((resolve, reject) => {
    const ytDlp = spawn(
      "python",
      [
        "-m",
        "yt_dlp",
        url,
        "--format",
        formatSelector,
        "--simulate",
        "--print",
        "%(format_id)s %(height)sp %(width)sx%(height)s %(ext)s %(vcodec)s",
        "--extractor-args",
        "youtube:player_client=android,formats=missing_pot",
        "--user-agent",
        "com.google.android.youtube/17.36.4 (Linux; U; Android 12; GB) gzip",
      ],
      {
        stdio: ["pipe", "pipe", "pipe"],
      }
    );

    let stdout = "";
    let stderr = "";

    ytDlp.stdout.on("data", (data) => {
      stdout += data.toString();
    });

    ytDlp.stderr.on("data", (data) => {
      stderr += data.toString();
    });

    ytDlp.on("close", (code) => {
      if (code === 0) {
        console.log("Selected format:", stdout.trim());
        resolve(stdout.trim());
      } else {
        console.error("Download test failed:", stderr);
        reject(new Error("Download test failed"));
      }
    });
  });
}

// Test with a sample YouTube video
const testUrl = "https://www.youtube.com/watch?v=hgtdiU3Bc1Q"; // Short test video

async function runTests() {
  try {
    console.log("=== YouTube Video Download Quality Test ===\n");

    await testVideoInfo(testUrl);
    await testDownload(testUrl, "1080p");
    await testDownload(testUrl, "720p");

    console.log("\n✅ All tests completed successfully!");
    console.log("\nThe fixes should now:");
    console.log("1. Extract real video quality information from YouTube");
    console.log("2. Prioritize highest quality downloads");
    console.log("3. Use better format selection logic");
    console.log("4. Fall back gracefully if highest quality is not available");
  } catch (error) {
    console.error("❌ Test failed:", error.message);
  }
}

runTests();
